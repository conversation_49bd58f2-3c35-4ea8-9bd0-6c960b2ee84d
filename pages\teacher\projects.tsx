// pages/teacher/projects.tsx
import React from 'react';

const TeacherProjectsPage = () => {
  return (
    <div className="p-6">
      <h1 className="text-3xl font-bold mb-6">Projects</h1>
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        <div className="bg-white dark:bg-gray-800 rounded-lg p-6 shadow-md">
          <h2 className="text-xl font-semibold mb-3">Math Assignment</h2>
          <p className="text-gray-600 dark:text-gray-300 mb-4">
            Algebra problems for Grade 9 students
          </p>
          <div className="flex justify-between items-center">
            <span className="text-sm text-gray-500">Due: Tomorrow</span>
            <button className="bg-blue-500 text-white px-4 py-2 rounded hover:bg-blue-600">
              View
            </button>
          </div>
        </div>
        
        <div className="bg-white dark:bg-gray-800 rounded-lg p-6 shadow-md">
          <h2 className="text-xl font-semibold mb-3">Science Project</h2>
          <p className="text-gray-600 dark:text-gray-300 mb-4">
            Chemistry lab experiment documentation
          </p>
          <div className="flex justify-between items-center">
            <span className="text-sm text-gray-500">Due: Next week</span>
            <button className="bg-blue-500 text-white px-4 py-2 rounded hover:bg-blue-600">
              View
            </button>
          </div>
        </div>
        
        <div className="bg-white dark:bg-gray-800 rounded-lg p-6 shadow-md">
          <h2 className="text-xl font-semibold mb-3">History Essay</h2>
          <p className="text-gray-600 dark:text-gray-300 mb-4">
            World War II research paper
          </p>
          <div className="flex justify-between items-center">
            <span className="text-sm text-gray-500">Due: Friday</span>
            <button className="bg-blue-500 text-white px-4 py-2 rounded hover:bg-blue-600">
              View
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default TeacherProjectsPage;
